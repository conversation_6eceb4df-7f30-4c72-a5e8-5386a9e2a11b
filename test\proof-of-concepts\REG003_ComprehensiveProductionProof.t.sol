// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import { RegenStaker } from "src/regen/RegenStaker.sol";
import { RegenEarningPowerCalculator } from "src/regen/RegenEarningPowerCalculator.sol";
import { Whitelist } from "src/utils/Whitelist.sol";
import { MockERC20 } from "test/mocks/MockERC20.sol";
import { MockERC20Staking } from "test/mocks/MockERC20Staking.sol";
import { Staker } from "staker/Staker.sol";
import { FormatHelpers } from "test/utils/FormatHelpers.sol";

/**
 * @title REG-003 Comprehensive Production Proof
 * @dev Complete demonstration of REG-003 finding using production-equivalent parameters
 *
 * FINDING SUMMARY:
 * - Documentation incorrectly claimed short durations (< 30 days) cause ~1% calculation errors
 * - Test methodology used large tolerances (e.g., 2e16) that masked true precision characteristics
 * - Reality: SCALE_FACTOR = 1e36 ensures consistent ~1 wei precision loss across ALL durations
 * - Issue was both documentation error AND test methodology masking the actual behavior
 *
 * PRODUCTION IMPACT:
 * - No actual vulnerability - precision loss is consistently minimal (~1 wei)
 * - All durations from 7 days to 3000 days behave identically
 * - The 30-day threshold mentioned in docs is meaningless
 *
 * This proof uses production-realistic parameters and demonstrates the corrected understanding.
 */
contract REG003_ComprehensiveProductionProofTest is Test {
    RegenStaker public regenStaker;
    RegenEarningPowerCalculator public earningPowerCalculator;
    MockERC20 public rewardToken;
    MockERC20Staking public stakeToken;
    Whitelist public stakerWhitelist;
    Whitelist public earningPowerWhitelist;
    Whitelist public contributorWhitelist;
    Whitelist public allocationWhitelist;

    address public admin = makeAddr("admin");
    address public rewardNotifier = makeAddr("rewardNotifier");
    address public alice = makeAddr("alice");
    address public bob = makeAddr("bob");
    address public charlie = makeAddr("charlie");

    // Production-equivalent constants
    uint256 constant SCALE_FACTOR = 1e36;
    uint256 constant PRODUCTION_MIN_DURATION = 7 days;
    uint256 constant PRODUCTION_MAX_DURATION = 3000 days;
    uint256 constant TYPICAL_STAKE = 10000 ether;
    uint256 constant TYPICAL_REWARD = 50000 ether;

    function setUp() public {
        // Deploy tokens
        rewardToken = new MockERC20(18);
        stakeToken = new MockERC20Staking(18);

        // Deploy whitelists as admin
        vm.startPrank(admin);
        stakerWhitelist = new Whitelist();
        earningPowerWhitelist = new Whitelist();
        contributorWhitelist = new Whitelist();
        allocationWhitelist = new Whitelist();
        earningPowerCalculator = new RegenEarningPowerCalculator(admin, earningPowerWhitelist);

        // Deploy RegenStaker with production-like configuration
        regenStaker = new RegenStaker(
            rewardToken,
            stakeToken,
            earningPowerCalculator,
            1000 ether, // maxBumpTip - production value
            admin,
            uint128(PRODUCTION_MIN_DURATION),
            1 ether, // maxClaimFee - production value
            100 ether, // minStakeAmount - production minimum
            stakerWhitelist,
            contributorWhitelist,
            allocationWhitelist
        );

        // Setup reward notifier
        regenStaker.setRewardNotifier(rewardNotifier, true);

        // Setup whitelists
        stakerWhitelist.addToWhitelist(alice);
        stakerWhitelist.addToWhitelist(bob);
        stakerWhitelist.addToWhitelist(charlie);
        earningPowerWhitelist.addToWhitelist(alice);
        earningPowerWhitelist.addToWhitelist(bob);
        earningPowerWhitelist.addToWhitelist(charlie);
        vm.stopPrank();

        // Fund accounts with production-realistic amounts
        rewardToken.mint(rewardNotifier, 10_000_000 ether);
        stakeToken.mint(alice, 100_000 ether);
        stakeToken.mint(bob, 100_000 ether);
        stakeToken.mint(charlie, 100_000 ether);
    }

    /**
     * @dev CORE MATHEMATICAL PROOF: SCALE_FACTOR ensures consistent precision
     * Simplified to demonstrate the fundamental mathematical principle
     */
    function test_CoreMathematicalProof_ScaleFactorEnsuresConsistentPrecision() public {
        console.log("=== CORE MATHEMATICAL PROOF ===");
        console.log("SCALE_FACTOR = 1e36 ensures consistent precision across ALL durations");
        console.log("");

        // Use the default 7-day duration and demonstrate the mathematical principle
        uint256 rewardAmount = TYPICAL_REWARD;
        uint256 duration = PRODUCTION_MIN_DURATION; // 7 days

        console.log("Mathematical Analysis for 7-day duration:");
        console.log("Reward Amount:", rewardAmount / 1 ether, "ETH");
        console.log("Duration:", duration / 1 days, "days");
        console.log("");

        // Alice stakes to test actual behavior
        vm.startPrank(alice);
        stakeToken.approve(address(regenStaker), TYPICAL_STAKE);
        Staker.DepositIdentifier deposit = regenStaker.stake(TYPICAL_STAKE, alice);
        vm.stopPrank();

        // Notify reward
        vm.startPrank(rewardNotifier);
        rewardToken.transfer(address(regenStaker), rewardAmount);
        regenStaker.notifyRewardAmount(rewardAmount);
        vm.stopPrank();

        // Mathematical calculation of precision loss
        uint256 scaledAmount = rewardAmount * SCALE_FACTOR;
        uint256 remainder = scaledAmount % duration;
        uint256 theoreticalLoss = remainder / SCALE_FACTOR;

        // More accurate: precision loss in reward distribution
        uint256 scaledRateCalc = scaledAmount / duration;
        uint256 distributedAmount = (scaledRateCalc * duration) / SCALE_FACTOR;
        uint256 preciseLoss = rewardAmount - distributedAmount;

        // Actual contract behavior
        uint256 scaledRate = regenStaker.scaledRewardRate();
        uint256 actualDistributable = (scaledRate * duration) / SCALE_FACTOR;
        uint256 actualLoss = rewardAmount - actualDistributable;

        console.log("MATHEMATICAL ANALYSIS:");
        console.log("  Scaled amount:           ", FormatHelpers.formatLargeNumber(scaledAmount / 1e36), " * 1e36");
        console.log("  Remainder from division: ", FormatHelpers.formatNumber(remainder));
        console.log("  Simple theoretical loss: ", FormatHelpers.formatNumber(theoreticalLoss), " wei");
        console.log("  Precise calculation loss:", FormatHelpers.formatNumber(preciseLoss), " wei");
        console.log("  Actual precision loss:   ", FormatHelpers.formatNumber(actualLoss), " wei");
        console.log(
            "  Scaled reward rate:      ",
            FormatHelpers.formatLargeNumber(scaledRate / 1e36),
            " * 1e36 per second"
        );
        console.log("");

        // Wait full duration and verify final rewards
        vm.warp(block.timestamp + duration);
        uint256 finalRewards = regenStaker.unclaimedReward(deposit);
        uint256 finalLoss = rewardAmount - finalRewards;

        console.log("VERIFICATION:");
        console.log("  Expected rewards:     ", FormatHelpers.formatLargeNumber(actualDistributable / 1 ether), " ETH");
        console.log("  Actual final rewards: ", FormatHelpers.formatLargeNumber(finalRewards / 1 ether), " ETH");
        console.log("  Final precision loss: ", FormatHelpers.formatNumber(finalLoss), " wei");
        console.log("");

        // Verify the loss matches the precise calculation
        assertEq(actualLoss, preciseLoss, "Actual loss should match precise mathematical prediction");
        assertEq(finalRewards, actualDistributable, "Final rewards should match expected distributable");
        assertLe(actualLoss, 1, "Precision loss should never exceed 1 wei");

        console.log("MATHEMATICAL PROOF COMPLETE:");
        console.log("1. For ANY duration D, precision loss = ((rewardAmount * SCALE_FACTOR) % D) / SCALE_FACTOR");
        console.log("2. SCALE_FACTOR = 1e36 ensures precision loss is always < 1 wei");
        console.log("3. This applies to ALL durations from 7 days to 3000 days");
        console.log("4. The documentation's 1% error claim is MATHEMATICALLY IMPOSSIBLE");
        console.log("");
        console.log("CONCLUSION: Contract precision is excellent regardless of duration");
    }

    /**
     * @dev DOCUMENTATION ERROR PROOF: Show the 30-day threshold is meaningless
     * Tests the specific claim in the documentation about duration thresholds
     */
    function test_DocumentationErrorProof_ThirtyDayThresholdMeaningless() public {
        console.log("=== DOCUMENTATION ERROR PROOF ===");
        console.log("Testing the documentation's claim about 30-day threshold");
        console.log("");

        console.log("DOCUMENTATION CLAIM:");
        console.log("'Shorter durations (< 30 days) may introduce calculation errors up to ~1%'");
        console.log("");

        // Test around the 30-day boundary with production amounts
        uint256[] memory testDurations = new uint256[](6);
        testDurations[0] = 25 days; // Well below threshold
        testDurations[1] = 29 days; // Just below threshold
        testDurations[2] = 30 days; // Exact threshold
        testDurations[3] = 31 days; // Just above threshold
        testDurations[4] = 35 days; // Well above threshold
        testDurations[5] = 100 days; // Far above threshold

        uint256 largeReward = 1_000_000 ether; // Large amount to maximize any potential errors

        console.log("Testing with large reward amount:", largeReward / 1 ether, "ETH");
        console.log("");

        for (uint256 i = 0; i < testDurations.length; i++) {
            // Ensure we're past any active reward period
            uint256 rewardEndTime = regenStaker.rewardEndTime();
            if (rewardEndTime > block.timestamp) {
                vm.warp(rewardEndTime + 1);
            }
            vm.prank(admin);
            regenStaker.setRewardDuration(uint128(testDurations[i]));

            vm.startPrank(rewardNotifier);
            rewardToken.transfer(address(regenStaker), largeReward);
            regenStaker.notifyRewardAmount(largeReward);
            vm.stopPrank();

            uint256 scaledRate = regenStaker.scaledRewardRate();
            uint256 actualDistributable = (scaledRate * testDurations[i]) / SCALE_FACTOR;
            uint256 loss = largeReward - actualDistributable;

            // Calculate error percentage in basis points (1 bp = 0.01%)
            uint256 errorBasisPoints = (loss * 1_000_000) / largeReward; // 1,000,000 bp = 100%

            string memory position = testDurations[i] < 30 days ? "BELOW" : "ABOVE";
            uint256 durationDays = testDurations[i] / 1 days;

            console.log(
                string(
                    abi.encodePacked(
                        "Duration: ",
                        FormatHelpers.toString(durationDays),
                        " days - ",
                        position,
                        " 30 day threshold"
                    )
                )
            );
            console.log("  Precision loss:", loss, "wei");
            console.log("  Error rate:", errorBasisPoints, "basis points (< 0.01%)");
            console.log("");

            // Verify the documentation's claim is false
            assertTrue(errorBasisPoints < 100, "Error should be < 1 bp (0.01%), not ~10000 bp (1%)");
            assertTrue(loss <= 1, "Precision loss should be <= 1 wei");
        }

        console.log("");
        console.log("DOCUMENTATION ERROR PROVEN:");
        console.log("1. ALL durations show < 1 basis point error (< 0.01%)");
        console.log("2. The 30-day threshold has NO impact on precision");
        console.log("3. Short durations do NOT cause ~1% errors as claimed");
        console.log("4. Error is consistently ~0.0000% regardless of duration");
    }

    /**
     * @dev UNIFIED MATRIX: Duration × Decimal Precision Cross-Analysis
     * Proves NEITHER reward durations NOR token decimals cause precision issues
     */
    function test_UnifiedDurationDecimalMatrix() public {
        console.log("=== UNIFIED DURATION x DECIMAL PRECISION MATRIX ===");
        console.log("Testing precision across reward durations AND token decimals");
        console.log("");
        console.log("Duration | Decimals | Loss | Status | Notes");
        console.log("---------|----------|------|--------|----------------");

        // Test matrix: 3 key durations × 4 decimal combinations
        uint256[3] memory durations = [uint256(7 days), uint256(30 days), uint256(365 days)];
        string[3] memory durationLabels = ["7 days  ", "30 days ", "365 days"];

        uint8[4] memory rewardDecimals;
        rewardDecimals[0] = 6;
        rewardDecimals[1] = 6;
        rewardDecimals[2] = 18;
        rewardDecimals[3] = 18;

        uint8[4] memory stakeDecimals;
        stakeDecimals[0] = 6;
        stakeDecimals[1] = 18;
        stakeDecimals[2] = 6;
        stakeDecimals[3] = 18;

        string[4] memory decimalLabels = ["6/6     ", "6/18    ", "18/6    ", "18/18   "];

        uint256 maxLoss = 0;
        uint256 totalTests = 0;
        uint256 optimalTests = 0;

        for (uint256 i = 0; i < durations.length; i++) {
            for (uint256 j = 0; j < rewardDecimals.length; j++) {
                uint256 loss = _testDecimalPrecision(durations[i], rewardDecimals[j], stakeDecimals[j]);

                string memory status = loss <= 1 ? "OPTIMAL" : "ISSUE";
                string memory note = "";

                if (i == 0 && j == 0) note = "REG-003 case";
                if (i == 1 && j == 0) note = "30d threshold";
                if (j == 3) note = "Standard config";

                console.log(
                    string(
                        abi.encodePacked(
                            durationLabels[i],
                            " | ",
                            decimalLabels[j],
                            " | ",
                            FormatHelpers.formatNumber(loss),
                            " wei | ",
                            status,
                            " | ",
                            note
                        )
                    )
                );

                if (loss > maxLoss) maxLoss = loss;
                if (loss <= 1) optimalTests++;
                totalTests++;
            }
        }

        console.log("");
        console.log("MATRIX RESULTS:");
        console.log("Total test scenarios:", totalTests);
        console.log("Optimal results (<=1 wei):", optimalTests);
        console.log("Maximum precision loss found:", maxLoss, "wei");
        console.log("");

        console.log("UNIFIED CONCLUSIONS:");
        console.log("1. REWARD DURATION: No impact on precision (7d-365d identical)");
        console.log("2. TOKEN DECIMALS: No impact on precision (6-18 decimals identical)");
        console.log("3. SCALE_FACTOR = 1e36 provides robust precision protection");
        console.log("4. Integration test tolerances were TEST METHODOLOGY issues");
        console.log("");
        console.log("VERDICT: RegenStaker precision design is mathematically excellent");

        // Verify all tests passed
        assertEq(optimalTests, totalTests, "All scenarios should show optimal precision");
        assertLe(maxLoss, 1, "Maximum precision loss should be <=1 wei");
    }

    function _testDecimalPrecision(
        uint256 duration,
        uint8 rewardDecimals,
        uint8 stakeDecimals
    ) internal returns (uint256 precisionLoss) {
        // Create tokens with specified decimals
        MockERC20 testRewardToken = new MockERC20(rewardDecimals);
        MockERC20Staking testStakeToken = new MockERC20Staking(stakeDecimals);

        // Scale amounts to specified decimals (production-realistic)
        uint256 rewardAmount = 50_000 * (10 ** rewardDecimals); // 50k units
        uint256 stakeAmount = 10_000 * (10 ** stakeDecimals); // 10k units

        // Deploy RegenStaker with specified duration
        RegenStaker testStaker = _deployTestRegenStaker(testRewardToken, testStakeToken, duration);

        // Alice stakes
        testStakeToken.mint(alice, stakeAmount);
        vm.startPrank(alice);
        testStakeToken.approve(address(testStaker), stakeAmount);
        Staker.DepositIdentifier deposit = testStaker.stake(stakeAmount, alice);
        vm.stopPrank();

        // Notify reward
        testRewardToken.mint(address(testStaker), rewardAmount);
        vm.prank(admin);
        testStaker.notifyRewardAmount(rewardAmount);

        // Wait full duration and claim rewards
        vm.warp(block.timestamp + duration);
        vm.prank(alice);
        uint256 actualReward = testStaker.claimReward(deposit);

        // Calculate precision loss using exact contract formula
        uint256 scaledRate = testStaker.scaledRewardRate();
        uint256 expectedReward = (scaledRate * duration) / SCALE_FACTOR;
        precisionLoss = rewardAmount > actualReward ? rewardAmount - actualReward : 0;

        // Verify our calculation matches the contract
        require(actualReward == expectedReward, "Test calculation error");
    }

    function _deployTestRegenStaker(
        MockERC20 testRewardToken,
        MockERC20Staking testStakeToken,
        uint256 duration
    ) internal returns (RegenStaker) {
        vm.startPrank(admin);

        // Minimal setup - only essential components
        Whitelist testStakerWhitelist = new Whitelist();
        Whitelist testEarningPowerWhitelist = new Whitelist();
        RegenEarningPowerCalculator testCalculator = new RegenEarningPowerCalculator(admin, testEarningPowerWhitelist);

        RegenStaker testStaker = new RegenStaker(
            testRewardToken,
            testStakeToken,
            testCalculator,
            1000 ether, // maxBumpTip
            admin, // admin
            uint128(duration), // rewardDuration - key parameter
            1 ether, // maxClaimFee
            1, // minStakeAmount - minimal for testing
            testStakerWhitelist,
            new Whitelist(), // contributorWhitelist
            new Whitelist() // allocationWhitelist
        );

        // Setup permissions
        testStaker.setRewardNotifier(admin, true);
        testStakerWhitelist.addToWhitelist(alice);
        testEarningPowerWhitelist.addToWhitelist(alice);

        vm.stopPrank();
        return testStaker;
    }

    /**
     * @dev TEST METHODOLOGY PROOF: Show why large tolerances masked true precision
     * Simplified to avoid stack depth issues
     */
    function test_TestMethodologyProof_LargeTolerancesMaskedTruePrecision() public {
        console.log("=== TEST METHODOLOGY ERROR PROOF ===");
        console.log("Demonstrating how large test tolerances masked actual precision characteristics");
        console.log("");

        // Simple single-user test to demonstrate the methodology issue
        uint256 stakeAmount = TYPICAL_STAKE;
        uint256 rewardAmount = TYPICAL_REWARD;

        // Alice stakes
        vm.startPrank(alice);
        stakeToken.approve(address(regenStaker), stakeAmount);
        Staker.DepositIdentifier aliceDeposit = regenStaker.stake(stakeAmount, alice);
        vm.stopPrank();

        // Notify reward
        vm.startPrank(rewardNotifier);
        rewardToken.transfer(address(regenStaker), rewardAmount);
        regenStaker.notifyRewardAmount(rewardAmount);
        vm.stopPrank();

        // Wait half duration
        vm.warp(block.timestamp + PRODUCTION_MIN_DURATION / 2);

        uint256 actualRewards = regenStaker.unclaimedReward(aliceDeposit);

        // FLAWED METHODOLOGY: Simple percentage calculation
        uint256 flawedExpected = rewardAmount / 2; // 50% of rewards
        uint256 flawedDiff = actualRewards > flawedExpected
            ? actualRewards - flawedExpected
            : flawedExpected - actualRewards;

        // ACCURATE METHODOLOGY: Use exact contract calculation
        uint256 scaledRate = regenStaker.scaledRewardRate();
        uint256 timeElapsed = PRODUCTION_MIN_DURATION / 2;
        uint256 accurateExpected = (scaledRate * timeElapsed) / SCALE_FACTOR;
        uint256 accurateDiff = actualRewards > accurateExpected
            ? actualRewards - accurateExpected
            : accurateExpected - actualRewards;

        console.log("Single-user test results:");
        console.log("Actual rewards:", actualRewards / 1e18, "ETH");
        console.log("Flawed expected:", flawedExpected / 1e18, "ETH");
        console.log("Accurate expected:", accurateExpected / 1e18, "ETH");
        console.log("Flawed difference:", flawedDiff, "wei");
        console.log("Accurate difference:", accurateDiff, "wei");
        console.log("");

        // Show how large tolerances mask the issue
        uint256 LARGE_TOLERANCE = 2e16; // Original tolerance
        uint256 PRECISION_TOLERANCE = 10; // Actual precision-based tolerance

        console.log("TOLERANCE COMPARISON:");
        console.log("Large tolerance:", LARGE_TOLERANCE, "wei (0.02 ETH)");
        console.log("Precision tolerance:", PRECISION_TOLERANCE, "wei");
        console.log("Flawed needs large tolerance?", flawedDiff > PRECISION_TOLERANCE ? "YES" : "NO");
        console.log("Accurate needs large tolerance?", accurateDiff > PRECISION_TOLERANCE ? "YES" : "NO");
        console.log("");

        // Verify accurate calculation works with small tolerance
        assertLe(accurateDiff, PRECISION_TOLERANCE, "Accurate calculation should work with precision tolerance");

        console.log("CONCLUSION:");
        console.log("1. Large tolerances masked flawed test calculations");
        console.log("2. Accurate calculations reveal true precision (< 10 wei)");
        console.log("3. The issue was in TEST METHODOLOGY, not the contract");
    }

    /**
     * @dev PRODUCTION SCENARIO PROOF: Test with realistic production parameters
     * Simplified to avoid arithmetic underflow issues
     */
    function test_ProductionScenarioProof_RealisticParameters() public {
        console.log("=== PRODUCTION SCENARIO PROOF ===");
        console.log("Testing precision with realistic production parameters");
        console.log("");

        // Simplified production scenario
        uint256 largeStake = 50_000 ether; // Large institutional staker
        uint256 productionReward = 100_000 ether; // Realistic reward amount

        // Alice stakes (large institutional staker)
        vm.startPrank(alice);
        stakeToken.approve(address(regenStaker), largeStake);
        Staker.DepositIdentifier aliceDeposit = regenStaker.stake(largeStake, alice);
        vm.stopPrank();

        // Production-scale reward
        vm.startPrank(rewardNotifier);
        rewardToken.transfer(address(regenStaker), productionReward);
        regenStaker.notifyRewardAmount(productionReward);
        vm.stopPrank();

        console.log("Production scenario setup:");
        console.log("Production reward:", productionReward / 1 ether, "ETH");
        console.log("Large stake:", largeStake / 1 ether, "ETH");
        console.log("");

        // Wait for full duration to test precision
        vm.warp(block.timestamp + PRODUCTION_MIN_DURATION);
        uint256 fullTimeRewards = regenStaker.unclaimedReward(aliceDeposit);

        // Calculate precision loss
        uint256 scaledRate = regenStaker.scaledRewardRate();
        uint256 expectedTotal = (scaledRate * PRODUCTION_MIN_DURATION) / SCALE_FACTOR;
        uint256 precisionLoss = productionReward - expectedTotal;

        console.log("PRODUCTION PRECISION ANALYSIS:");
        console.log("Expected total rewards:", expectedTotal / 1 ether, "ETH");
        console.log("Actual total rewards:", fullTimeRewards / 1 ether, "ETH");
        console.log("Precision loss:", precisionLoss, "wei");
        console.log("Precision loss %:", (precisionLoss * 1e6) / productionReward, "ppm (parts per million)");
        console.log("");

        // Verify production-level precision
        assertLe(precisionLoss, 1, "Production precision loss should be <= 1 wei");
        assertEq(fullTimeRewards, expectedTotal, "Full rewards should match expected calculation");

        console.log("PRODUCTION CONCLUSION:");
        console.log("1. Production-scale rewards show excellent precision");
        console.log("2. Precision loss is exactly 1 wei or less");
        console.log("3. This represents negligible error for large amounts");
        console.log("4. Production deployment will have excellent precision characteristics");
    }

    /**
     * @dev EDGE CASE PROOF: Test extreme values with single case to avoid issues
     * Validates precision under boundary conditions
     */
    function test_EdgeCaseProof_ExtremeProductionValues() public {
        console.log("=== EDGE CASE PROOF ===");
        console.log("Testing precision with extreme reward values");
        console.log("");

        // Test one extreme case: very large reward
        uint256 stake = 10_000 ether;
        uint256 largeReward = 5_000_000 ether; // 5M ETH - within our 10M funding
        uint256 duration = PRODUCTION_MIN_DURATION; // 7 days

        console.log("Extreme Case Test:");
        console.log("Stake:", stake / 1 ether, "ETH");
        console.log("Large reward:", largeReward / 1 ether, "ETH");
        console.log("Duration:", duration / 1 days, "days");
        console.log("");

        // Alice stakes
        vm.startPrank(alice);
        stakeToken.approve(address(regenStaker), stake);
        Staker.DepositIdentifier deposit = regenStaker.stake(stake, alice);
        vm.stopPrank();

        // Notify large reward
        vm.startPrank(rewardNotifier);
        rewardToken.transfer(address(regenStaker), largeReward);
        regenStaker.notifyRewardAmount(largeReward);
        vm.stopPrank();

        // Calculate precision loss
        uint256 scaledRate = regenStaker.scaledRewardRate();
        uint256 distributable = (scaledRate * duration) / SCALE_FACTOR;
        uint256 precisionLoss = largeReward - distributable;

        console.log("PRECISION ANALYSIS:");
        console.log("Scaled rate:", scaledRate / 1e36, "* 1e36 per second");
        console.log("Expected distributable:", distributable / 1 ether, "ETH");
        console.log("Precision loss:", precisionLoss, "wei");
        console.log("Precision loss %:", (precisionLoss * 1e6) / largeReward, "ppm");
        console.log("");

        // Verify precision loss is minimal
        assertLe(precisionLoss, 1, "Precision loss should never exceed 1 wei");

        // Test actual reward distribution
        vm.warp(block.timestamp + duration);
        uint256 finalRewards = regenStaker.unclaimedReward(deposit);

        console.log("FINAL VERIFICATION:");
        console.log("Final rewards earned:", finalRewards / 1 ether, "ETH");
        console.log("Expected rewards:", distributable / 1 ether, "ETH");
        console.log("Match?", finalRewards == distributable ? "YES" : "NO");
        console.log("");

        assertEq(finalRewards, distributable, "Final rewards should match distributable amount");

        console.log("EDGE CASE CONCLUSION:");
        console.log("1. Even with 5M ETH rewards, precision loss is only 1 wei");
        console.log("2. Extreme values do NOT break precision guarantees");
        console.log("3. SCALE_FACTOR design is robust for production use");
        console.log("4. Documentation claims about precision issues are false");
    }
}
