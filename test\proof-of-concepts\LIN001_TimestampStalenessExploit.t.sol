// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import { Test } from "forge-std/Test.sol";
import { console } from "forge-std/console.sol";
import { LinearAllowanceSingletonForGnosisSafe } from "src/zodiac-core/modules/LinearAllowanceSingletonForGnosisSafe.sol";
import { MockSafe } from "test/mocks/zodiac-core/MockSafe.sol";
import { NATIVE_TOKEN } from "src/constants.sol";

/**
 * @title LIN-001: Timestamp Staleness Vulnerability Exploit
 * @dev Demonstrates two manifestations of the conditional timestamp update bug at line 231
 *
 * ROOT CAUSE: `else if (newAccrued > 0)` fails to update timestamps when newAccrued == 0
 *
 * MANIFESTATION 1: Zero rates fail to stop accrual (allowance keeps growing despite zero rate)
 * MANIFESTATION 2: Rate increases apply retroactively (new rates backdated to stale timestamps)
 *
 * Both are the SAME BUG with different exploitable symptoms.
 * SEVERITY: High - Direct fund extraction via timestamp manipulation
 */
contract TimestampStalenessExploitTest is Test {
    LinearAllowanceSingletonForGnosisSafe internal allowanceModule;
    MockSafe internal mockSafe;
    address internal delegate = makeAddr("delegate");
    address payable internal recipient = payable(makeAddr("recipient"));

    function setUp() public {
        allowanceModule = new LinearAllowanceSingletonForGnosisSafe();
        mockSafe = new MockSafe();
        mockSafe.enableModule(address(allowanceModule));
        vm.deal(address(mockSafe), 100 ether);
    }

    /**
     * @dev MANIFESTATION 1: Zero rate fails to stop accrual
     * Expected: Setting rate to zero immediately stops allowance accumulation
     * Actual: Allowance continues growing despite zero rate due to stale timestamp
     */
    function testZeroRateFailsToStopAccrual() public {
        uint192 rate = uint192(1 ether); // 1 ETH per day

        // 1. Setup normal allowance and accumulate some allowance
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, rate);
        vm.warp(block.timestamp + 6 hours); // Accumulate 0.25 ETH

        // 2. Transfer to consume the accumulated allowance and update timestamp
        vm.prank(delegate);
        uint256 consumed = allowanceModule.executeAllowanceTransfer(address(mockSafe), NATIVE_TOKEN, recipient);
        console.log("Initial transfer consumed:", consumed);

        // 3. Create very small rate that will round to zero newAccrued
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, 1); // 1 wei per day

        // 4. Wait a very short time that rounds to zero accrual - this creates staleness
        vm.warp(block.timestamp + 10); // 10 seconds with 1 wei/day = rounds to 0

        // 5. Set rate back to high value - timestamp should update but won't due to staleness
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, rate); // Back to 1 ETH/day

        // 6. Set rate to ZERO - this should stop accrual, but timestamp is stale
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, 0);

        // 7. Time passes - with stale timestamp, this might still accrue at old rate
        vm.warp(block.timestamp + 6 hours);

        // 8. Check if allowance stopped accumulating (should be 0 with zero rate)
        uint256 balanceBefore = recipient.balance;
        vm.prank(delegate);

        try allowanceModule.executeAllowanceTransfer(address(mockSafe), NATIVE_TOKEN, recipient) returns (uint256) {
            uint256 extracted = recipient.balance - balanceBefore;

            console.log("Extracted with zero rate after 6h:", extracted);

            if (extracted > 0.01 ether) {
                console.log("BUG WOULD BE PRESENT: Zero rate would fail to stop accrual");
                console.log("Would extract", extracted, "wei despite zero rate");
                assertTrue(false, "Vulnerability not fixed - zero rate failed to stop accrual");
            } else {
                console.log("FIX CONFIRMED: Zero rate correctly stopped accrual");
                console.log("Minimal extraction shows fix is working");
                assertTrue(true, "Fix correctly handles zero rate edge case");
            }
        } catch {
            console.log("FIX CONFIRMED: No allowance available - zero rate worked correctly");
            console.log("Timestamp properly updated, preventing stale accrual");
            assertTrue(true, "Fix correctly blocks all transfers with zero rate");
        }
    }

    /**
     * @dev MANIFESTATION 2: Rate increases apply retroactively
     * Expected: New rates only apply from the moment they are set forward
     * Actual: New rates are backdated to stale timestamps, creating retroactive allowance
     */
    function testRateIncreasesApplyRetroactively() public {
        uint192 initialRate = uint192(1 ether); // 1 ETH per day
        uint192 newRate = uint192(3 ether); // 3 ETH per day

        // 1. Setup initial rate and let time pass
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, initialRate);
        vm.warp(block.timestamp + 6 hours);

        // 2. Transfer current accrual to reset allowance state
        vm.prank(delegate);
        uint256 transfer1 = allowanceModule.executeAllowanceTransfer(address(mockSafe), NATIVE_TOKEN, recipient);

        // 3. Create timestamp staleness condition via low rate rounding
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, 1); // 1 wei per day
        vm.warp(block.timestamp + 50); // 50ms - rounds to 0, creates staleness

        // 4. New rate should start applying from this point forward

        // 5. Set new higher rate - this should only apply going forward
        vm.prank(address(mockSafe));
        allowanceModule.setAllowance(delegate, NATIVE_TOKEN, newRate);

        // 6. Wait only 1 hour at new rate
        vm.warp(block.timestamp + 1 hours);

        // 7. Check if new rate was applied retroactively
        uint256 balanceBefore = recipient.balance;
        vm.prank(delegate);
        allowanceModule.executeAllowanceTransfer(address(mockSafe), NATIVE_TOKEN, recipient);
        uint256 extracted = recipient.balance - balanceBefore;

        // Expected: Only 1h at new rate = ~0.125 ETH
        uint256 expectedLegitimate = (1 hours * newRate) / 1 days;

        console.log("Initial transfer (6h at 1 ETH/day):", transfer1);
        console.log("Expected for 1h at new rate:", expectedLegitimate);
        console.log("Actually extracted:", extracted);
        console.log("Retroactive excess:", extracted > expectedLegitimate ? extracted - expectedLegitimate : 0);

        if (extracted > expectedLegitimate + 0.001 ether) {
            console.log("BUG WOULD BE PRESENT: Rate increase would apply retroactively");
            console.log("New rate would be backdated to stale timestamp");
            assertTrue(false, "Vulnerability not fixed - rate increases still apply retroactively");
        } else {
            console.log("FIX CONFIRMED: Rate increase applied correctly (forward-only)");
            console.log("Timestamp is properly updated, preventing retroactive application");
            assertTrue(true, "Fix correctly prevents retroactive rate application");
        }
    }

    /**
     * @dev Root cause analysis showing both manifestations stem from same bug
     */
    function testRootCauseAnalysis() public pure {
        console.log("=== LIN-001 ROOT CAUSE ANALYSIS ===");
        console.log("Location: LinearAllowanceSingletonForGnosisSafe.sol:231");
        console.log("Bug: else if (newAccrued > 0) { a.lastBookedAtInSeconds = timestamp; }");
        console.log("Issue: Conditional timestamp updates fail when newAccrued == 0");
        console.log("");

        console.log("TWO MANIFESTATIONS OF SAME BUG:");
        console.log("");

        console.log("MANIFESTATION 1: Zero rates fail to stop accrual");
        console.log("- Expected: Zero rate immediately stops allowance accumulation");
        console.log("- Actual: Allowance continues growing despite zero rate");
        console.log("- Cause: Stale timestamp makes calculations use old non-zero rate");
        console.log("");

        console.log("MANIFESTATION 2: Rate increases apply retroactively");
        console.log("- Expected: New rates only apply from moment they're set");
        console.log("- Actual: New rates backdated to stale timestamp periods");
        console.log("- Cause: Same stale timestamp, but now with higher rate");
        console.log("");

        console.log("SINGLE ROOT CAUSE:");
        console.log("When newAccrued == 0 (zero rates, rounding, short periods),");
        console.log("timestamp is NOT updated, leading to stale timestamp calculations");
        console.log("");

        console.log("SEVERITY: High - Both manifestations enable direct fund extraction");
        console.log("FIX: Always update timestamp regardless of newAccrued value");
    }
}
