name: ▶️ Create New Release
on:
  workflow_dispatch:

concurrency:
  group: "${{ github.ref }}-create-release"
  cancel-in-progress: false

jobs:
  access-check:
    name: Check access-rights
    runs-on: general
    if: github.ref_name == 'develop' || github.ref_name == 'main'
    steps:
      - name: Check pre-requisites
        run: |
          ALLOWED_USERS=("0xartem" "qGolem" "mslomnicki")
          echo "Triggered by ${{ github.actor }}"
          if [[ ! " ${ALLOWED_USERS[@]} " =~ " ${{ github.actor }} " ]]; then
            echo "❌ Unauthorized user: ${{ github.actor }}"
            exit 1
          fi
          echo "✅ Authorized user: ${{ github.actor }}"

  publish-prerelease:
    name: Publish Prerelease
    needs:
      - access-check
    if: github.ref_name == 'develop'
    runs-on: general
    permissions:
      contents: write
      id-token: write
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_BOT_TOKEN }}
          fetch-depth: 0

      - name: Set git signing
        shell: bash
        run: |
          yarn config set -H npmScopes.golemfoundation.npmAuthToken "${{ secrets.HOUSEKEEPER_PAT_OCTANT_V2_PACKAGES }}"
          gpg --import <(echo "${{ secrets.HOUSEKEEPER_GITHUB_SIGNING_KEY }}" | base64 -d)
          git config --global --add safe.directory "$GITHUB_WORKSPACE"
          git config --local user.email "<EMAIL>"
          git config --local user.name "Housekeeper Bot"
          git config --local user.signingkey "${{ secrets.HOUSEKEEPER_GITHUB_SIGNING_KEY_ID }}"

      - name: Init env
        run: |
          yarn install
          forge soldeer install

      - name: Publish preRelease
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          yarn release --ci --preRelease=develop

  publish-release:
    name: Publish Release
    needs:
      - access-check
    if: github.ref_name == 'main'
    runs-on: general
    permissions:
      contents: write
      id-token: write
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_BOT_TOKEN }}
          fetch-depth: 0

      - name: Set git signing
        shell: bash
        run: |
          yarn config set -H npmScopes.golemfoundation.npmAuthToken "${{ secrets.HOUSEKEEPER_PAT_OCTANT_V2_PACKAGES }}"
          gpg --import <(echo "${{ secrets.HOUSEKEEPER_GITHUB_SIGNING_KEY }}" | base64 -d)
          git config --global --add safe.directory "$GITHUB_WORKSPACE"
          git config --local user.email "<EMAIL>"
          git config --local user.name "Housekeeper Bot"
          git config --local user.signingkey "${{ secrets.HOUSEKEEPER_GITHUB_SIGNING_KEY_ID }}"

      - name: Init env
        run: |
          yarn install
          forge soldeer install

      - name: Publish release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          yarn release --ci
