# In steps following image build we're ignoring files uploaded to /_w/ directory as current code is in /app directory

name: 🤖 Push to develop
on:
  push:
    branches:
      - develop

concurrency:
  group: "build-image"
  cancel-in-progress: false

jobs:
  lint-and-format:
    name: <PERSON><PERSON> and test
    uses: ./.github/workflows/tpl-lint.yml
    with:
      GCP_DOCKER_IMAGE_REGISTRY: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}
    secrets: inherit

  build-image:
    name: Build Docker Image
    needs:
      - lint-and-format
    runs-on:
      - general
    steps:
      - uses: actions/checkout@v4
        with:
          # use some obscure path to checkout the code with service account perms
          # this is possible as $GITHUB_WORKSPACE is owned by the same service
          # account
          # see: https://github.com/actions/checkout/issues/211
          path: __local

      - name: Login to Docker registry
        uses: docker/login-action@v3
        with:
          registry: europe-docker.pkg.dev
          username: _json_key_base64
          password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/core
          tags: |
            type=ref,event=branch
            type=sha,prefix=,format=long

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: __local
          file: __local/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/cache:core
          cache-to: type=registry,ref=${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/cache:core,mode=max

  test:
    name: Test suite
    needs: build-image
    runs-on: general
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/core:${{ github.sha }}
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    permissions:
      contents: read
      actions: read
      checks: write
    steps:
      - name: Run tests
        env:
          TEST_RPC_URL: "${{ vars.TEST_MAINNET_RPC_URL }}"
          TEST_SAFE_SINGLETON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
          TEST_RPC_URL_POLYGON: "https://polygon-rpc.com"
          TEST_SAFE_SINGLETON_POLYGON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY_POLYGON: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
        run: "cd /app; yarn test:ci"

      - name: Upload test artifact
        uses: actions/upload-artifact@v4  # upload test results
        if: success() || failure()        # run this step even if previous step failed
        with:
          name: test-results
          path: /app/reports/junit.xml

      - name: Prepare test report
        uses: dorny/test-reporter@v1
        if: success() || failure()        # run this step even if previous step failed
        with:
          artifact: test-results            # artifact name
          name: Tests results                # Name of the check run which will be created
          path: '*.xml'                     # Path to test results (inside artifact .zip)
          reporter: java-junit              # Format of test results
