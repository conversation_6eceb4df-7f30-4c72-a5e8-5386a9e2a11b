# In steps following image build we're ignoring files uploaded to /_w/ directory as current code is in /app directory

name: 🤖 CI Pull Request
on:
  pull_request:
    branches:
      - main
      - master
      - develop

concurrency:
  group: "pr-${{ github.sha }}"
  cancel-in-progress: true

jobs:
  lint-and-format:
    name: Lint and test
    uses: ./.github/workflows/tpl-lint.yml
    with:
      GCP_DOCKER_IMAGE_REGISTRY: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}
    secrets: inherit

  build:
    name: Build
    needs:
      - lint-and-format
    runs-on: general
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Init env
        run: |
          yarn install
          forge soldeer install

      - name: Build
        run: yarn build

  test:
    name: Test suite
    needs: build
    runs-on: general
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    permissions:
      contents: read
      actions: read
      checks: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Init env
        run: |
          yarn install
          forge soldeer install

      - name: Run tests
        env:
          TEST_RPC_URL: "${{ vars.TEST_MAINNET_RPC_URL }}"
          TEST_SAFE_SINGLETON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
          TEST_RPC_URL_POLYGON: "https://polygon-rpc.com"
          TEST_SAFE_SINGLETON_POLYGON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY_POLYGON: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
        run: yarn test:ci

      - name: Upload test artifact
        uses: actions/upload-artifact@v4  # upload test results
        if: success() || failure()        # run this step even if previous step failed
        with:
          name: test-results
          path: reports/junit.xml

      - name: Prepare test report
        uses: dorny/test-reporter@v1
        if: success() || failure()        # run this step even if previous step failed
        with:
          artifact: test-results            # artifact name
          name: Tests results                # Name of the check run which will be created
          path: '*.xml'                     # Path to test results (inside artifact .zip)
          reporter: java-junit              # Format of test results

  coverage:
    name: Code Coverage
    needs:
      - build
    runs-on: general
    container:
      image: ${{ vars.GCP_DOCKER_IMAGE_REGISTRY }}/node-extended:f4f41461
      credentials:
        username: _json_key_base64
        password: ${{ secrets.GCP_DOCKER_IMAGES_REGISTRY_SERVICE_ACCOUNT }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Init env
        run: | 
          yarn install
          forge soldeer install

      - name: Generate Coverage Report
        env:
          TEST_RPC_URL: "${{ vars.TEST_MAINNET_RPC_URL }}"
          TEST_SAFE_SINGLETON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
          TEST_RPC_URL_POLYGON: "https://polygon-rpc.com"
          TEST_SAFE_SINGLETON_POLYGON: "0x41675C099F32341bf84BFc5382aF534df5C7461a"
          TEST_SAFE_PROXY_FACTORY_POLYGON: "0x4e1DCf7AD4e460CfD30791CCC4F9c8a4f820ec67"
        run: |
          # Comment out problematic lines
          sed -i.bak '197,205s/^/\/\/ /' dependencies/hats-protocol-1.0/src/Hats.sol
          
          # Create markdown report header
          echo "# Code Coverage Report for src/ files" > src_coverage.md
          echo "" >> src_coverage.md
          echo "| File | % Lines | % Statements | % Branches | % Funcs |" >> src_coverage.md
          echo "|------|---------|--------------|------------|---------|" >> src_coverage.md
          
          # Run coverage
          echo "Running coverage with --ir-minimum, this may take a while..."
          if forge coverage --report summary --no-match-test "Skip" --ir-minimum > full_coverage.txt 2>coverage_error.txt; then
            echo "Coverage completed successfully"
            
            # Process each src/ line and add emoji indicators
            grep "| src/" full_coverage.txt | while read -r line; do
              # Extract file path
              file=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
              
              # Extract numerical percentages and full data
              lines_data=$(echo "$line" | awk -F'|' '{print $3}' | xargs)
              stmts_data=$(echo "$line" | awk -F'|' '{print $4}' | xargs)
              branch_data=$(echo "$line" | awk -F'|' '{print $5}' | xargs)
              funcs_data=$(echo "$line" | awk -F'|' '{print $6}' | xargs)
              
              # Extract just the percentage values (before the %)
              lines_pct=$(echo "$lines_data" | grep -o "^[0-9]\+\.[0-9]\+" || echo "0")
              stmts_pct=$(echo "$stmts_data" | grep -o "^[0-9]\+\.[0-9]\+" || echo "0")
              branch_pct=$(echo "$branch_data" | grep -o "^[0-9]\+\.[0-9]\+" || echo "0")
              funcs_pct=$(echo "$funcs_data" | grep -o "^[0-9]\+\.[0-9]\+" || echo "0")
              
              # Compare as integers by removing decimal part (85.00 becomes 85)
              lines_int=$(echo "$lines_pct" | cut -d. -f1)
              stmts_int=$(echo "$stmts_pct" | cut -d. -f1)
              branch_int=$(echo "$branch_pct" | cut -d. -f1)
              funcs_int=$(echo "$funcs_pct" | cut -d. -f1)
              
              # Add emojis using integer comparison
              if [ "$lines_int" -lt 85 ]; then
                lines_formatted="🔴 $lines_data"
              else
                lines_formatted="✅ $lines_data"
              fi
              
              if [ "$stmts_int" -lt 85 ]; then
                stmts_formatted="🔴 $stmts_data"
              else
                stmts_formatted="✅ $stmts_data"
              fi
              
              if [ "$branch_int" -lt 85 ]; then
                branch_formatted="🔴 $branch_data"
              else
                branch_formatted="✅ $branch_data"
              fi
              
              if [ "$funcs_int" -lt 85 ]; then
                funcs_formatted="🔴 $funcs_data"
              else
                funcs_formatted="✅ $funcs_data"
              fi
              
              # Output the formatted line
              echo "| $file | $lines_formatted | $stmts_formatted | $branch_formatted | $funcs_formatted |" >> src_coverage.md
            done
          else
            echo "Coverage failed with exit code $?"
            echo "## ⚠️ Coverage Generation Failed" >> src_coverage.md
            echo "" >> src_coverage.md
            echo "Error output:" >> src_coverage.md
            echo '```' >> src_coverage.md
            cat coverage_error.txt >> src_coverage.md
            echo '```' >> src_coverage.md
          fi
          
          # Restore original file
          mv lib/hats-protocol/src/Hats.sol.bak lib/hats-protocol/src/Hats.sol
          
          # Always output what we have to GitHub step summary
          cat src_coverage.md >> $GITHUB_STEP_SUMMARY
        continue-on-error: true

      - name: Add PR Comment
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: coverage-report
          path: src_coverage.md
