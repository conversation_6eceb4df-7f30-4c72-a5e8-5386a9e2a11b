/* SPDX-License-Identifier: GPL-3.0 */

pragma solidity ^0.8.23;

import { Script } from "forge-std/Script.sol";
import { ERC20Mock } from "@openzeppelin/contracts/mocks/token/ERC20Mock.sol";
import { IUniswapV3Pool } from "../../src/utils/vendor/uniswap/IUniswapV3Pool.sol";
import { IUniswapV3Factory } from "../../src/utils/vendor/uniswap/IUniswapV3Factory.sol";

contract HelperConfig is Script {
    address public constant ETH = ******************************************;

    struct UniswapPoolConfig {
        address base;
        address quote;
        uint24 fee;
    }

    struct NetworkConfig {
        address glmToken;
        address wethToken;
        address nonfungiblePositionManager;
        uint256 deployerKey;
        address uniswapV3Router;
        address trader;
        address swapperFactory;
        address oracleFactory;
        address uniV3Swap;
        address uniswapV3Factory;
    }

    // This is the private key for dev account available in anvil. It is not a secret!
    uint256 public immutable DEFAULT_ANVIL_KEY = 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80;
    NetworkConfig public activeNetworkConfig;
    mapping(string => UniswapPoolConfig) public poolByName;
    string[] public pools;

    constructor(bool forking) {
        if (block.chainid == 1) {
            // running on mainnet or on a mainnet fork
            activeNetworkConfig = initMainnetEthConfig(forking);
        }
        if (block.chainid == ********) {
            activeNetworkConfig = initSepoliaETHConfig(forking);
        } else {
            activeNetworkConfig = createAnvilEthConfig();
        }
        vm.label(activeNetworkConfig.glmToken, "glmToken");
        vm.label(activeNetworkConfig.wethToken, "wethToken");
        vm.label(activeNetworkConfig.nonfungiblePositionManager, "nonfungiblePositionManager");
        vm.label(activeNetworkConfig.uniswapV3Router, "uniswapV3Router");
        vm.label(activeNetworkConfig.swapperFactory, "swapperFactory");
        vm.label(activeNetworkConfig.oracleFactory, "oracleFactory");
        vm.label(activeNetworkConfig.uniV3Swap, "uniV3Swap");
        vm.label(activeNetworkConfig.uniswapV3Factory, "uniswapV3Factory");
    }

    function initSepoliaETHConfig(bool forking) internal returns (NetworkConfig memory) {
        uint256 deployerKey;
        if (forking) {
            // throw-away environment; using ETH from test account
            deployerKey = DEFAULT_ANVIL_KEY;
        } else {
            // some other environment, user needs to provide their own source of ETH for gas
            deployerKey = vm.envUint("PRIVATE_KEY");
        }

        addPool("ETHGLM", ETH, ******************************************, 10_000); // ETH -> GLM, 1%
        addPool("GLMETH", ******************************************, ETH, 10_000); // GLM -> ETH, 1%

        return
            // UniswapV3Factory at ******************************************
            NetworkConfig({
                glmToken: ******************************************,
                wethToken: ******************************************,
                nonfungiblePositionManager: ******************************************,
                uniswapV3Router: ******************************************,
                deployerKey: deployerKey,
                trader: ******************************************,
                swapperFactory: ******************************************,
                oracleFactory: ******************************************,
                uniV3Swap: ******************************************,
                uniswapV3Factory: ******************************************
            });
    }

    function initMainnetEthConfig(bool forking) internal returns (NetworkConfig memory) {
        uint256 deployerKey;
        if (forking) {
            deployerKey = DEFAULT_ANVIL_KEY;
        } else {
            deployerKey = vm.envUint("PRIVATE_KEY");
        }

        addPool("ETHGLM", ETH, ******************************************, 10_000); // ETH -> GLM, 1%
        addPool("GLMETH", ******************************************, ETH, 10_000); // ETH -> GLM, 1%

        return
            NetworkConfig({
                glmToken: ******************************************,
                wethToken: ******************************************,
                nonfungiblePositionManager: ******************************************,
                uniswapV3Router: ******************************************,
                deployerKey: deployerKey,
                trader: address(0),
                swapperFactory: ******************************************,
                oracleFactory: ******************************************,
                uniV3Swap: ******************************************,
                uniswapV3Factory: ******************************************
            });
    }

    function addPool(string memory _name, address _base, address _quote, uint24 _fee) internal {
        UniswapPoolConfig memory pool = UniswapPoolConfig({ base: _base, quote: _quote, fee: _fee });
        pools.push(_name);
        poolByName[_name] = pool;
    }

    function poolCount() public view returns (uint) {
        return pools.length;
    }

    function getPoolAddress(address _base, address _quote, uint24 _fee) public returns (address) {
        IUniswapV3Factory factory = IUniswapV3Factory(activeNetworkConfig.uniswapV3Factory);
        return factory.getPool(_base, _quote, _fee);
    }

    function createAnvilEthConfig() internal returns (NetworkConfig memory) {
        if (activeNetworkConfig.glmToken != address(0)) {
            return activeNetworkConfig;
        }

        vm.startBroadcast();

        ERC20Mock wethMock = new ERC20Mock();
        ERC20Mock glmMock = new ERC20Mock();

        vm.stopBroadcast();

        return
            NetworkConfig({
                glmToken: address(glmMock),
                wethToken: address(wethMock),
                nonfungiblePositionManager: address(0), // deploy
                uniswapV3Router: address(0),
                deployerKey: DEFAULT_ANVIL_KEY,
                trader: address(0),
                swapperFactory: address(0),
                oracleFactory: address(0),
                uniV3Swap: address(0),
                uniswapV3Factory: address(0)
            });
    }
}
